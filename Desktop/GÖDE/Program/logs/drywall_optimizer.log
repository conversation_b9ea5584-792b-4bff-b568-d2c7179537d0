2025-07-01 20:36:02,745 - __main__ - INFO - Starting Drywall Optimization Application
2025-07-01 20:36:02,745 - utils.validation - ERROR - Required module reportlab is not available
2025-07-01 20:36:02,745 - utils.validation - ERROR - Missing required modules: ['reportlab']
2025-07-01 20:52:40,793 - __main__ - INFO - Starting Drywall Optimization Application
2025-07-01 20:52:41,588 - utils.validation - INFO - Environment validation successful
2025-07-01 20:52:41,620 - ui.panels.materials - INFO - Materials panel initialized
2025-07-01 20:52:41,621 - ui.panels.properties - WARNING - Failed to load snap settings: 'MainWindow' object has no attribute 'snap_label'
2025-07-01 20:52:41,621 - ui.panels.properties - INFO - Properties panel initialized
2025-07-01 20:52:41,621 - ui.canvas.interactions - INFO - <PERSON>agManager initialized
2025-07-01 20:52:41,624 - ui.canvas.infinite_canvas - INFO - Infinite canvas initialized
2025-07-01 20:52:41,624 - ui.main_window - INFO - Main window initialized
2025-07-01 20:52:41,624 - __main__ - INFO - Application started successfully
2025-07-01 20:53:16,532 - __main__ - INFO - Application closed
2025-07-01 21:00:36,198 - __main__ - INFO - Starting Drywall Optimization Application
2025-07-01 21:00:36,466 - utils.validation - INFO - Environment validation successful
2025-07-01 21:00:36,481 - root - ERROR - Fatal error: 'MainWindow' object has no attribute 'config'
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/GÖDE/Program/main.py", line 52, in main
    app = MainWindow(root)
          ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/GÖDE/Program/ui/main_window.py", line 46, in __init__
    cutting_config = self.config.get('cutting', {})
                     ^^^^^^^^^^^
AttributeError: 'MainWindow' object has no attribute 'config'
2025-07-01 21:01:44,907 - __main__ - INFO - Starting Drywall Optimization Application
2025-07-01 21:01:45,244 - utils.validation - INFO - Environment validation successful
2025-07-01 21:01:45,260 - root - ERROR - Fatal error: 'MainWindow' object has no attribute 'config'
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/GÖDE/Program/main.py", line 52, in main
    app = MainWindow(root)
          ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/GÖDE/Program/ui/main_window.py", line 46, in __init__
    cutting_config = self.config.get('cutting', {})
                     ^^^^^^^^^^^
AttributeError: 'MainWindow' object has no attribute 'config'
2025-07-01 21:43:44,277 - __main__ - INFO - Starting Drywall Optimization Application
2025-07-01 21:43:44,797 - utils.validation - INFO - Environment validation successful
2025-07-01 21:43:44,813 - core.cutting.engine - INFO - CuttingEngine initialized with precision=0.1mm
2025-07-01 21:43:44,814 - core.cutting.metadata - INFO - MetadataTracker initialized
2025-07-01 21:43:44,814 - export.pdf.pdf_exporter - INFO - PDFExporter initialized with page size (595.2755905511812, 841.8897637795277)
2025-07-01 21:43:44,815 - persistence.project_serializer - INFO - ProjectSerializer initialized
2025-07-01 21:43:44,817 - persistence.json_handler - INFO - JSONProjectHandler initialized
2025-07-01 21:43:44,817 - core.performance.memory_manager - INFO - MemoryManager initialized with cache size 1000
2025-07-01 21:43:44,817 - core.performance.performance_monitor - INFO - PerformanceMonitor initialized with history size 1000
2025-07-01 21:43:44,837 - ui.panels.materials - WARNING - Failed to load default sheets: 'NoneType' object has no attribute 'get'
2025-07-01 21:43:44,838 - ui.panels.materials - INFO - Materials panel initialized
2025-07-01 21:43:44,838 - ui.panels.properties - WARNING - Failed to load snap settings: 'NoneType' object has no attribute 'get'
2025-07-01 21:43:44,838 - ui.panels.properties - INFO - Properties panel initialized
2025-07-01 21:43:44,838 - ui.canvas.interactions - INFO - DragManager initialized
2025-07-01 21:43:44,839 - ui.canvas.infinite_canvas - INFO - Infinite canvas initialized
2025-07-01 21:43:44,840 - ui.main_window - INFO - Main window initialized
2025-07-01 21:43:44,840 - __main__ - INFO - Application started successfully
2025-07-01 21:47:13,758 - __main__ - INFO - Starting Drywall Optimization Application
2025-07-01 21:47:14,288 - utils.validation - INFO - Environment validation successful
2025-07-01 21:47:14,303 - core.cutting.engine - INFO - CuttingEngine initialized with precision=0.1mm
2025-07-01 21:47:14,303 - core.cutting.metadata - INFO - MetadataTracker initialized
2025-07-01 21:47:14,304 - export.pdf.pdf_exporter - INFO - PDFExporter initialized with page size (595.2755905511812, 841.8897637795277)
2025-07-01 21:47:14,304 - persistence.project_serializer - INFO - ProjectSerializer initialized
2025-07-01 21:47:14,304 - persistence.json_handler - INFO - JSONProjectHandler initialized
2025-07-01 21:47:14,304 - core.performance.memory_manager - INFO - MemoryManager initialized with cache size 1000
2025-07-01 21:47:14,304 - core.performance.performance_monitor - INFO - PerformanceMonitor initialized with history size 1000
2025-07-01 21:47:14,320 - ui.panels.materials - WARNING - Failed to load default sheets: 'NoneType' object has no attribute 'get'
2025-07-01 21:47:14,320 - ui.panels.materials - INFO - Materials panel initialized
2025-07-01 21:47:14,320 - ui.panels.properties - WARNING - Failed to load snap settings: 'NoneType' object has no attribute 'get'
2025-07-01 21:47:14,321 - ui.panels.properties - INFO - Properties panel initialized
2025-07-01 21:47:14,321 - ui.canvas.interactions - INFO - DragManager initialized
2025-07-01 21:47:14,321 - ui.canvas.infinite_canvas - INFO - Infinite canvas initialized
2025-07-01 21:47:14,321 - ui.main_window - INFO - Main window initialized
2025-07-01 21:47:14,321 - __main__ - INFO - Application started successfully
2025-07-01 21:48:31,793 - ui.main_window - INFO - Status: Added 12 sheets of size 1200.0×2000.0 mm
2025-07-01 21:48:33,265 - ui.main_window - INFO - Status: Added sheet 1200.0×2000.0 to canvas
2025-07-01 21:48:38,314 - ui.main_window - INFO - Status: Added sheet 1200.0×2000.0 to canvas
2025-07-01 21:48:41,216 - ui.main_window - INFO - Status: Added sheet 1200.0×2000.0 to canvas
2025-07-01 21:53:46,220 - __main__ - INFO - Starting Drywall Optimization Application
2025-07-01 21:53:47,124 - utils.validation - INFO - Environment validation successful
2025-07-01 21:53:47,142 - core.cutting.engine - INFO - CuttingEngine initialized with precision=0.1mm
2025-07-01 21:53:47,142 - core.cutting.metadata - INFO - MetadataTracker initialized
2025-07-01 21:53:47,143 - export.pdf.pdf_exporter - INFO - PDFExporter initialized with page size (595.2755905511812, 841.8897637795277)
2025-07-01 21:53:47,143 - persistence.project_serializer - INFO - ProjectSerializer initialized
2025-07-01 21:53:47,144 - persistence.json_handler - INFO - JSONProjectHandler initialized
2025-07-01 21:53:47,144 - core.performance.memory_manager - INFO - MemoryManager initialized with cache size 1000
2025-07-01 21:53:47,144 - core.performance.performance_monitor - INFO - PerformanceMonitor initialized with history size 1000
2025-07-01 21:53:47,173 - ui.panels.materials - WARNING - Failed to load default sheets: 'NoneType' object has no attribute 'get'
2025-07-01 21:53:47,173 - ui.panels.materials - INFO - Materials panel initialized
2025-07-01 21:53:47,174 - ui.panels.properties - WARNING - Failed to load snap settings: 'NoneType' object has no attribute 'get'
2025-07-01 21:53:47,174 - ui.panels.properties - INFO - Properties panel initialized
2025-07-01 21:53:47,174 - ui.canvas.interactions - INFO - DragManager initialized
2025-07-01 21:53:47,175 - ui.canvas.infinite_canvas - INFO - Infinite canvas initialized
2025-07-01 21:53:47,175 - ui.main_window - INFO - Main window initialized
2025-07-01 21:53:47,175 - __main__ - INFO - Application started successfully
2025-07-01 21:54:56,429 - __main__ - INFO - Application closed
2025-07-01 21:56:57,279 - __main__ - INFO - Starting Drywall Optimization Application
2025-07-01 21:56:58,216 - utils.validation - INFO - Environment validation successful
2025-07-01 21:56:58,237 - core.cutting.engine - INFO - CuttingEngine initialized with precision=0.1mm
2025-07-01 21:56:58,237 - core.cutting.metadata - INFO - MetadataTracker initialized
2025-07-01 21:56:58,238 - export.pdf.pdf_exporter - INFO - PDFExporter initialized with page size (595.2755905511812, 841.8897637795277)
2025-07-01 21:56:58,238 - persistence.project_serializer - INFO - ProjectSerializer initialized
2025-07-01 21:56:58,240 - persistence.json_handler - INFO - JSONProjectHandler initialized
2025-07-01 21:56:58,240 - core.performance.memory_manager - INFO - MemoryManager initialized with cache size 1000
2025-07-01 21:56:58,240 - core.performance.performance_monitor - INFO - PerformanceMonitor initialized with history size 1000
2025-07-01 21:56:58,266 - ui.panels.materials - WARNING - Failed to load default sheets: 'NoneType' object has no attribute 'get'
2025-07-01 21:56:58,267 - ui.panels.materials - INFO - Materials panel initialized
2025-07-01 21:56:58,267 - ui.panels.properties - WARNING - Failed to load snap settings: 'NoneType' object has no attribute 'get'
2025-07-01 21:56:58,267 - ui.panels.properties - INFO - Properties panel initialized
2025-07-01 21:56:58,268 - ui.canvas.interactions - INFO - DragManager initialized
2025-07-01 21:56:58,269 - ui.canvas.infinite_canvas - INFO - Infinite canvas initialized
2025-07-01 21:56:58,269 - ui.main_window - INFO - Main window initialized
2025-07-01 21:56:58,269 - __main__ - INFO - Application started successfully
2025-07-01 21:57:24,955 - __main__ - INFO - Application closed
2025-07-01 21:57:47,052 - __main__ - INFO - Starting Drywall Optimization Application
2025-07-01 21:57:47,483 - utils.validation - INFO - Environment validation successful
2025-07-01 21:57:47,515 - core.cutting.engine - INFO - CuttingEngine initialized with precision=0.1mm
2025-07-01 21:57:47,515 - core.cutting.metadata - INFO - MetadataTracker initialized
2025-07-01 21:57:47,516 - export.pdf.pdf_exporter - INFO - PDFExporter initialized with page size (595.2755905511812, 841.8897637795277)
2025-07-01 21:57:47,516 - persistence.project_serializer - INFO - ProjectSerializer initialized
2025-07-01 21:57:47,516 - persistence.json_handler - INFO - JSONProjectHandler initialized
2025-07-01 21:57:47,516 - core.performance.memory_manager - INFO - MemoryManager initialized with cache size 1000
2025-07-01 21:57:47,516 - core.performance.performance_monitor - INFO - PerformanceMonitor initialized with history size 1000
2025-07-01 21:57:47,532 - ui.panels.materials - WARNING - Failed to load default sheets: 'NoneType' object has no attribute 'get'
2025-07-01 21:57:47,532 - ui.panels.materials - INFO - Materials panel initialized
2025-07-01 21:57:47,533 - ui.panels.properties - WARNING - Failed to load snap settings: 'NoneType' object has no attribute 'get'
2025-07-01 21:57:47,533 - ui.panels.properties - INFO - Properties panel initialized
2025-07-01 21:57:47,533 - ui.canvas.interactions - INFO - DragManager initialized
2025-07-01 21:57:47,533 - ui.canvas.infinite_canvas - INFO - Infinite canvas initialized
2025-07-01 21:57:47,534 - ui.main_window - INFO - Main window initialized
2025-07-01 21:57:47,534 - __main__ - INFO - Application started successfully
2025-07-01 21:58:32,905 - __main__ - INFO - Application closed
