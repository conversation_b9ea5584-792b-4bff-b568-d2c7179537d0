"""
DXF file parser using ezdxf library.
"""

import logging
from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path
import ezdxf
from ezdxf.document import Drawing
from ezdxf.entities import LWPolyline, Polyline, Line, Arc, Circle
from shapely.geometry import <PERSON>ygon, LineString, Point as ShapelyPoint
from shapely.ops import linemerge, polygonize
import math

from ..geometry.shapes import Template, Point


logger = logging.getLogger(__name__)


class DXFParser:
    """Parser for DXF files to extract closed polylines as templates."""
    
    def __init__(self):
        """Initialize DXF parser."""
        self.units_scale = 1.0  # Scale factor for unit conversion
        self.supported_entities = ['LWPOLYLINE', 'POLYLINE', 'LINE', 'ARC', 'CIRCLE']
        self.tolerance = 1e-6  # Tolerance for connecting line endpoints
    
    def parse_file(self, file_path: str, target_layer: Optional[str] = None, 
                   units: str = 'mm') -> Tuple[List[Template], Dict[str, Any]]:
        """
        Parse a DXF file and extract templates.
        
        Args:
            file_path: Path to the DXF file
            target_layer: Specific layer to extract from (None for all layers)
            units: Target units for coordinates ('mm', 'cm', 'm', 'inch', 'ft')
            
        Returns:
            Tuple of (templates_list, metadata_dict)
        """
        try:
            # Load DXF document
            doc = ezdxf.readfile(file_path)
            logger.info(f"Successfully loaded DXF file: {file_path}")
            
            # Set up unit conversion
            self._setup_units(doc, units)
            
            # Extract metadata
            metadata = self._extract_metadata(doc, file_path)
            
            # Get target layers
            layers_to_process = self._get_target_layers(doc, target_layer)
            
            # Extract templates from each layer
            templates = []
            for layer_name in layers_to_process:
                layer_templates = self._extract_templates_from_layer(doc, layer_name)
                templates.extend(layer_templates)
            
            logger.info(f"Extracted {len(templates)} templates from DXF file")
            return templates, metadata
            
        except Exception as e:
            logger.error(f"Failed to parse DXF file {file_path}: {e}")
            raise
    
    def _setup_units(self, doc: Drawing, target_units: str) -> None:
        """
        Set up unit conversion scale factor.
        
        Args:
            doc: DXF document
            target_units: Target units for output
        """
        # Get DXF units (if available)
        dxf_units = 'mm'  # Default assumption
        
        # Try to get units from DXF header
        try:
            units_var = doc.header.get('$INSUNITS', 4)  # 4 = millimeters
            units_map = {
                1: 'inch',
                2: 'ft', 
                4: 'mm',
                5: 'cm',
                6: 'm'
            }
            dxf_units = units_map.get(units_var, 'mm')
        except:
            logger.warning("Could not determine DXF units, assuming millimeters")
        
        # Calculate scale factor
        self.units_scale = self._calculate_scale_factor(dxf_units, target_units)
        logger.info(f"Unit conversion: {dxf_units} -> {target_units}, scale: {self.units_scale}")
    
    def _calculate_scale_factor(self, from_units: str, to_units: str) -> float:
        """Calculate scale factor between units."""
        # Convert everything to mm first, then to target
        to_mm = {
            'mm': 1.0,
            'cm': 10.0,
            'm': 1000.0,
            'inch': 25.4,
            'ft': 304.8
        }
        
        from_mm = {
            'mm': 1.0,
            'cm': 0.1,
            'm': 0.001,
            'inch': 1/25.4,
            'ft': 1/304.8
        }
        
        # Scale factor = (from_units -> mm) * (mm -> to_units)
        return to_mm.get(from_units, 1.0) * from_mm.get(to_units, 1.0)
    
    def _extract_metadata(self, doc: Drawing, file_path: str) -> Dict[str, Any]:
        """Extract metadata from DXF document."""
        metadata = {
            'source_file': str(Path(file_path).name),
            'full_path': str(file_path),
            'dxf_version': doc.dxfversion,
            'layers': [],
            'entity_counts': {},
            'bounds': None
        }
        
        # Get layer information
        for layer in doc.layers:
            metadata['layers'].append({
                'name': layer.dxf.name,
                'color': layer.dxf.color,
                'linetype': layer.dxf.linetype
            })
        
        # Count entities by type
        msp = doc.modelspace()
        for entity in msp:
            entity_type = entity.dxftype()
            metadata['entity_counts'][entity_type] = metadata['entity_counts'].get(entity_type, 0) + 1
        
        return metadata
    
    def _get_target_layers(self, doc: Drawing, target_layer: Optional[str]) -> List[str]:
        """Get list of layers to process."""
        if target_layer:
            return [target_layer] if target_layer in [layer.dxf.name for layer in doc.layers] else []
        else:
            return [layer.dxf.name for layer in doc.layers]
    
    def _extract_templates_from_layer(self, doc: Drawing, layer_name: str) -> List[Template]:
        """Extract templates from a specific layer."""
        templates = []
        msp = doc.modelspace()

        # Get entities from the specified layer
        layer_entities = [entity for entity in msp if entity.dxf.layer == layer_name]

        # Separate LINE entities from other entities
        line_entities = [entity for entity in layer_entities if entity.dxftype() == 'LINE']
        other_entities = [entity for entity in layer_entities if entity.dxftype() != 'LINE']

        # Process non-LINE entities normally
        for entity in other_entities:
            try:
                polygon = self._entity_to_polygon(entity)
                if polygon and polygon.is_valid and polygon.exterior.is_closed:
                    template = Template(
                        polygon=polygon,
                        metadata={
                            'layer': layer_name,
                            'entity_type': entity.dxftype(),
                            'source_file': doc.filename or 'unknown'
                        }
                    )
                    templates.append(template)
                    logger.debug(f"Created template from {entity.dxftype()} on layer {layer_name}")
            except Exception as e:
                logger.warning(f"Failed to process entity {entity.dxftype()} on layer {layer_name}: {e}")

        # Process LINE entities to form closed polygons
        if line_entities:
            line_polygons = self._lines_to_polygons(line_entities)
            for polygon in line_polygons:
                if polygon and polygon.is_valid and polygon.exterior.is_closed:
                    template = Template(
                        polygon=polygon,
                        metadata={
                            'layer': layer_name,
                            'entity_type': 'LINE_POLYGON',
                            'source_file': doc.filename or 'unknown',
                            'line_count': len(line_entities)
                        }
                    )
                    templates.append(template)
                    logger.debug(f"Created template from connected lines on layer {layer_name}")

        return templates

    def _lines_to_polygons(self, line_entities: List[Line]) -> List[Polygon]:
        """Convert a collection of LINE entities to closed polygons."""
        polygons = []

        try:
            # Convert lines to LineString objects
            linestrings = []
            for line in line_entities:
                start = (line.dxf.start.x * self.units_scale, line.dxf.start.y * self.units_scale)
                end = (line.dxf.end.x * self.units_scale, line.dxf.end.y * self.units_scale)
                linestrings.append(LineString([start, end]))

            # Use polygonize to find closed polygons from the lines
            polygon_collection = polygonize(linestrings)

            for polygon in polygon_collection:
                if polygon.is_valid and polygon.area > 1.0:  # Minimum area threshold
                    polygons.append(polygon)
                    logger.debug(f"Found closed polygon from lines with area {polygon.area:.2f}")

            logger.info(f"Created {len(polygons)} polygons from {len(line_entities)} lines")

        except Exception as e:
            logger.warning(f"Failed to create polygons from lines: {e}")

        return polygons

    def _entity_to_polygon(self, entity) -> Optional[Polygon]:
        """Convert a DXF entity to a Shapely polygon."""
        try:
            if entity.dxftype() == 'LWPOLYLINE':
                return self._lwpolyline_to_polygon(entity)
            elif entity.dxftype() == 'POLYLINE':
                return self._polyline_to_polygon(entity)
            elif entity.dxftype() == 'CIRCLE':
                return self._circle_to_polygon(entity)
            elif entity.dxftype() == 'LINE':
                # Individual lines are not closed shapes, handled separately
                logger.debug(f"LINE entity will be processed with other lines to form polygons")
                return None
            # Add more entity types as needed
            else:
                logger.debug(f"Unsupported entity type: {entity.dxftype()}")
                return None
        except Exception as e:
            logger.warning(f"Failed to convert {entity.dxftype()} to polygon: {e}")
            return None
    
    def _lwpolyline_to_polygon(self, lwpolyline: LWPolyline) -> Optional[Polygon]:
        """Convert LWPOLYLINE to polygon."""
        if not lwpolyline.closed:
            return None
        
        points = []
        for point in lwpolyline.get_points():
            x, y = point[0] * self.units_scale, point[1] * self.units_scale
            points.append((x, y))
        
        if len(points) < 3:
            return None
        
        # Ensure polygon is closed
        if points[0] != points[-1]:
            points.append(points[0])
        
        return Polygon(points)
    
    def _polyline_to_polygon(self, polyline: Polyline) -> Optional[Polygon]:
        """Convert POLYLINE to polygon."""
        if not polyline.is_closed:
            return None
        
        points = []
        for vertex in polyline.vertices:
            x, y = vertex.dxf.location[0] * self.units_scale, vertex.dxf.location[1] * self.units_scale
            points.append((x, y))
        
        if len(points) < 3:
            return None
        
        # Ensure polygon is closed
        if points[0] != points[-1]:
            points.append(points[0])
        
        return Polygon(points)
    
    def _circle_to_polygon(self, circle: Circle, segments: int = 32) -> Polygon:
        """Convert CIRCLE to polygon approximation."""
        center = circle.dxf.center
        radius = circle.dxf.radius * self.units_scale
        
        points = []
        for i in range(segments):
            angle = 2 * math.pi * i / segments
            x = (center[0] + radius * math.cos(angle)) * self.units_scale
            y = (center[1] + radius * math.sin(angle)) * self.units_scale
            points.append((x, y))
        
        # Close the polygon
        points.append(points[0])
        
        return Polygon(points)
